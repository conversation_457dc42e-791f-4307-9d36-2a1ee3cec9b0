import os
import pandas as pd
from typing import List, Dict

class Column:
    ORIGINAL_COLUMN_NAME = "original_column_name"
    COLUMN_NAME = "column_name"
    COLUMN_DESCRIPTION = "column_description"
    VALUE_DESCRIPTION = "value_description"
    DATA_FORMAT = "data_format"
    EVIDENCE = "commonsense evidence:"

    def __init__(
            self,
            name: str, 
            data_type: str, 
            is_nullable: bool, 
            column_default: str, 
            description: List[str] = None,
            full_name: str = "",
            unique_values: List[str] = [],
            unique_count: int = 0,
            is_categorical: bool = False
    ) -> None:
        self.name: str = name
        self.data_type: str = data_type
        self.is_nullable: bool = is_nullable
        self.column_default: str = column_default
        self.description: List[str] = description if description is not None else []
        self.full_name: str = full_name
        self.unique_values: List[str] = unique_values
        self.unique_count: int = unique_count
        self.is_categorical: bool = is_categorical

    def to_dict(self):
        returned_dict = {}
        if self.name is not None and len(self.name) > 0:
            returned_dict["column_name"] = self.name
        if self.data_type is not None and len(self.data_type) > 0:
            returned_dict["data_type"] = self.data_type
        
        if self.description is not None and len(self.description) > 0:
            returned_dict["description"] = self.description
        
        if self.full_name is not None and len(self.full_name) > 0:
            returned_dict["full_name"] = self.full_name

        if self.unique_values is not None and len(self.unique_values) > 0:
            returned_dict["unique_values"] = self.unique_values
        
        return returned_dict

class ForeignKey:
    def __init__(
            self, 
            name: str, 
            src_table: str, 
            src_col: str, 
            dst_table: str, 
            dst_col: str, 
            description: str="",
            relation_type: str = "",
            confidence: float = 1.0
        ) -> None:
        self.name: str = name
        self.src_table: str = src_table
        self.src_col: str = src_col
        self.dst_table: str = dst_table
        self.dst_col: str = dst_col
        self.description: str = description
        self.relation_type: str = relation_type
        self.confidence: float = confidence

    def to_dict(self):
        return self.__dict__

class Table:
    def __init__(self, name: str, columns: List[Column], foreign_keys: List[ForeignKey], description: List[str] = None, evidences: List[Dict[str, str]] = None) -> None:
        self.name: str = name
        self.columns: List[Column] = columns
        self.foreign_keys: List[ForeignKey] = foreign_keys
        self.description: List[str] = description if description is not None else []
        self.evidences: List[Dict[str, str]] = evidences if evidences is not None else []

    def to_dict(self):
        return self.__dict__


class BaseDatabase:
    def get_table_schema(self, table_names: List[str] = None) -> List[Table]:
        raise NotImplementedError

    def execute_query(self, query: str) -> pd.DataFrame:
        raise NotImplementedError

    def get_dialect(self) -> str:
        raise NotImplementedError

    def load_database_knowledge_desc(self) -> str:
        raise NotImplementedError

    def get_knowledge(self, table_names: List[str] = None, n_sample_values: int = 50) -> List[Table]:
        raise NotImplementedError

    @staticmethod
    def load_table_description(table_description_path: str) -> Table:
        desc_df = pd.read_csv(table_description_path)
        desc_df.fillna("", inplace=True)
        evidences = BaseDatabase.load_evidence(desc_df)
        column_descriptions = BaseDatabase.load_column_descriptions(desc_df)
        # for col, evidence in evidences.items():
        return Table(
            name="",
            columns=column_descriptions,
            foreign_keys=[],
            description=[],
            evidences=evidences
        )
    
    @staticmethod
    def load_column_descriptions(desc_df: pd.DataFrame) -> List[Column]:
        columns = []
        for i, row in desc_df.iterrows():
            desc = row[Column.VALUE_DESCRIPTION]
            if Column.EVIDENCE in desc:
                desc = desc.split(Column.EVIDENCE)[0].strip()

            desc = [_desc.strip().replace('\xa0', '') for _desc in desc.split("\n") if len(_desc.strip()) > 0]

            column = Column(
                name=row[Column.ORIGINAL_COLUMN_NAME].strip(),
                data_type=row[Column.DATA_FORMAT].strip(),
                is_nullable=None,
                column_default=None,
                description=desc,
                full_name=row[Column.COLUMN_NAME].strip(),
            )
            columns.append(column)
        return columns

    @staticmethod
    def load_evidence(desc_df: pd.DataFrame) -> List[Dict[str, str]]:
        evidences = []
        for i, row in desc_df.iterrows():
            desc = row[Column.VALUE_DESCRIPTION].strip()
            if Column.EVIDENCE in desc:
                _evidences = desc.split(Column.EVIDENCE, 1)[-1].strip()
                _evidences = [_evi.strip().replace('\xa0', '') for _evi in _evidences.split("\n") if len(_evi.strip()) > 0]
                evidences.append(
                    {
                        "column_name": row[Column.ORIGINAL_COLUMN_NAME].strip(),
                        "evidence": "\n".join(_evidences)
                    }
                )

        return evidences
