import psycopg2

# Connection parameters
conn = psycopg2.connect(
    host="localhost",
    port=5432,
    database="chinook",
    user="postgres",
    password="mysecretpassword"
)

# Create a cursor
cur = conn.cursor()



from jinja2 import Template

foreign_key_template = Template(
    """
    {%for foreign_key in foreign_keys%}
      - Each record in **{{foreign_key[0]}}** is linked to a record in **{{foreign_key[2]}}** using:  
            + {{foreign_key[0]}}.{{foreign_key[1]}} → {{foreign_key[2]}}.{{foreign_key[3]}}
    {% endfor %}
    """
)

table_schema_temple = Template(
    """
    {%for table in tables%}
    {{table}}:
    {% for col in tables[table] %}\t- {{col[0]}}: {{col[1]}}
    {% endfor %}
    {% endfor %}
    """
)



s = table_schema_temple.render(
    tables={
        'table1':[
        (1, 2),
        (2, 4)
    ]
    }
)

s = foreign_key_template.render(
    foreign_keys=[
        (1, 2, 3, 4),
        (2, 3, 4, 5)
    ]
    
)
print(s)

cur.execute("""
    SELECT table_name
    FROM information_schema.tables
    WHERE table_schema = 'public';
""")

tables = cur.fetchall()
tables = [table[0] for table in tables]
tables


conn.rollback()

table_info = {}

for table in tables:
    table_info[table] = []
    cur.execute(f"""
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = '{table}';
    """)
    cols = []
    for row in cur.fetchall():

        table_info[table].append(
            (row[0], row[1])
        )



print(table_info)

row

foreign_key_info = []

cur.execute("""
    SELECT
        tc.constraint_name,
        tc.table_name AS source_table,
        kcu.column_name AS source_column,
        ccu.table_name AS target_table,
        ccu.column_name AS target_column
    FROM 
        information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
        AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
        AND ccu.table_schema = tc.table_schema
    WHERE tc.constraint_type = 'FOREIGN KEY';
""")


for row in cur.fetchall():
    foreign_key_info.append(
        row[1:]
    )


foreign_key_info

tables = table_schema_temple.render(
    tables=table_info
)

foreign_keys = foreign_key_template.render(
    foreign_keys=foreign_key_info
)

row

import yaml

res = """
 {
  "planning":
    "Step 1: Identify the relevant tables. The customer table contains customer names, and the invoice table contains order (invoice) records with dates and customer IDs.
    Step 2: Determine how to link the tables. Use the foreign key relationship: invoice.customer_id → customer.customer_id.
    Step 3: Filter the invoices to only those with an invoice_date in the year 2023.
    Step 4: From the filtered invoices, retrieve the associated customer records.
    Step 5: Select the customer names (first_name and last_name) from these customer records.
    Step 6: Ensure that each customer is listed only once, even if they placed multiple orders in 2023 (use DISTINCT or GROUP BY as appropriate)."
}
"""

import json

rs = yaml.safe_load(res)
print(rs)


#  curl https://5e10-57-155-66-184.ngrok-free.app/v1/chat/completions \
#   -H "Content-Type: application/json" \
#   -d '{
#        "model": "Qwen/Qwen2.5-Coder-7B-Instruct-GPTQ-Int4",
#        "messages":[{"role":"user","content":"Hello"}]
#      }'


from src.processor.databases.sqlite_database import SQLiteDatabase
from src.domain.database_config import DatabaseConfig

db_path = "/home/<USER>/Working/sources/VinAI/services/vinit-central-ai/services/sql-generator-platform/data/schema/bird/dev_databases/california_schools/california_schools.sqlite"

from src.processor.databases.sqlite_database import SQLiteDatabase
from src.domain.database_config import DatabaseConfig

database_config = DatabaseConfig(
    dialect="SQLite",
    host="localhost",
    port=5432,
    database=db_path,
    user="postgres",
    password="mysecretpassword",
    table_description_dir="/home/<USER>/Working/sources/VinAI/services/vinit-central-ai/services/sql-generator-platform/data/schema/bird/dev_databases/california_schools/database_description"
)
db = SQLiteDatabase(
    database_config,
    queries={
            "tables": """
      SELECT name 
      FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%';
    """,

    "table_schema": """
      PRAGMA table_info({{table_name}});
    """,

    "foreign_keys": """
      PRAGMA foreign_key_list({{table_name}});
    """,

    "get_unique_column_values": """
      SELECT DISTINCT `{{column_name}}`
      FROM {{table_name}}
      LIMIT {{limit}};
    """
    }
)





for fk in kd[2].foreign_keys:
    print(f"Foreign Key: {fk.src_table}.{fk.src_col} → {fk.dst_table}.{fk.dst_col}")

kl_str = db.build_table_description(kd)

print(kl_str)

q1 = """
SELECT
  s.City,
  s.School,
  f."Low Grade"
FROM
  schools s
  LEFT JOIN frpm f ON s.CDSCode = f.CDSCode
WHERE
  s.State = 'CA'
  AND s.Latitude = (
    SELECT MIN(Latitude)
    FROM schools
    WHERE State = 'CA'
  )
;
"""

q2 = """
SELECT T2.City,
       T1.`Low Grade`,
       T1.`School Name`
FROM frpm AS T1
INNER JOIN schools AS T2 ON T1.CDSCode = T2.CDSCode
WHERE T2.State = 'CA'
  AND T2.Latitude IS NOT NULL
  AND T2.Latitude =
    (SELECT MIN(Latitude)
     FROM schools
     WHERE State = 'CA'
       AND Latitude IS NOT NULL)
"""