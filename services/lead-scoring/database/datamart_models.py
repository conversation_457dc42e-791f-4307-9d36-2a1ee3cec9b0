import json
import time
import yaml
from typing import Tuple

from infi.clickhouse_orm import Model, StringField, Int32Field, Int64Field, Float32Field, Float64Field, \
    ReplacingMergeTree, Int16Field, ArrayField, Int8Field



class VhmPropertyDmModel(Model):
    @classmethod
    def table_name(cls) -> str:
        return "vhm_search_property"

    property_id = StringField()
    vhm_id = StringField()
    house_name = StringField()
    alias = StringField()
    alias_en = StringField()
    title = StringField()
    name = StringField()
    full_name = StringField()
    service_type = Int16Field()
    business_type = Int64Field()
    property_type = Int16Field()
    distribution_channel = Int16Field()
    channel = StringField()
    project_id = StringField()
    area_cluster_id = StringField()
    building_id = StringField()
    tower_id = StringField()
    delivery_type = Int64Field()
    house_style = Int64Field()
    architectural_style = Int32Field()
    height_type = Int32Field()
    sale_discount_vnd = Float32Field()
    rent_discount_vnd = Float32Field()
    sale_price_vnd = Float32Field()
    rent_price_vnd = Float32Field()
    num_bed_room = Int32Field()
    num_bath_room = Int32Field()
    floor_number = Int32Field()
    floors = Int32Field()
    direction = Int32Field()
    balcony_direction = Int32Field()
    area = Float32Field()
    area_using = Float32Field()
    area_building = Float32Field()
    alleyway_width = Float32Field()
    width = Float32Field()
    length = Float32Field()
    corner = Int16Field()
    road_crash = Int16Field()
    ownership = StringField()
    amenities = StringField()
    furniture = StringField()
    furniture_status = StringField()
    key_lock_number = StringField()
    juridical_status = Int32Field()
    exclusive = Int8Field()
    is_hot = Int8Field()
    has_3d = Int8Field()
    has_media = Int8Field()
    has_photo = Int8Field()
    content_assignee = StringField()
    content_assigner = StringField()
    request_content_time = Int64Field()

    content_status = Int32Field()
    media_assignee = StringField()
    media_assigner = StringField()
    media_status = StringField()
    rent_status = Int32Field()
    rent_info = StringField()
    sale_status = Int32Field()
    house_status = Int32Field()
    property_status = Int32Field()
    transaction_stage = Int32Field()
    core = StringField()
    address = StringField()
    contact = StringField()
    transaction = StringField()
    additional_info = StringField()
    tags = StringField()
    system_tags = StringField()
    client_id = StringField()
    sap_id = StringField()
    creator = StringField()
    published_time = Int64Field()
    created_time = Int64Field()
    updated_time = Int64Field()
    log_time = Int64Field()


    engine = ReplacingMergeTree(
        ver_col='log_time',
        partition_key=['toYYYYMM(created_time)'],
        order_by=[property_id],
        primary_key=[property_id]
    )

    def to_property_info(self) -> PropertyInfo:
        additional_info = AdditionalInfo.from_dict(yaml.safe_load(str(self.additional_info)))
        area, area_using, area_building = self._get_area_values(additional_info)
        width, length = self._get_width_length_values(additional_info)
        floors = self._get_floors_value(additional_info)
        sale_price, rent_price = self._get_price_values(additional_info)
        return PropertyInfo(
            property_id=self.property_id,
            vhm_id=self.vhm_id,
            house_name=self.house_name,
            alias=self.alias,
            alias_en=self.alias_en,
            title=self.title,
            name=self.name,
            full_name=self.full_name,
            service_type=self.service_type,
            business_type=self.business_type,
            property_type=self.property_type,
            distribution_channel=self.distribution_channel,
            channel=self.channel,
            project_id=self.project_id,
            area_cluster_id=self.area_cluster_id,
            building_id=self.building_id,
            tower_id=self.tower_id,
            delivery_type=self.delivery_type,
            house_style=self.house_style,
            architectural_style=self.architectural_style,
            height_type=self.height_type,
            sale_discount_vnd=self.sale_discount_vnd,
            rent_discount_vnd=self.rent_discount_vnd,
            sale_price_vnd=sale_price,
            rent_price_vnd=rent_price,
            num_bed_room=self.num_bed_room,
            num_bath_room=self.num_bath_room,
            floor_number=self.floor_number,
            floors=floors,
            direction=self.direction,
            balcony_direction=self.balcony_direction,
            area=area,
            area_using=area_using,
            area_building=area_building,
            alleyway_width=self.alleyway_width,
            width=width,
            length=length,
            corner=self.corner,
            road_crash=self.road_crash,
            ownership=self.ownership,
            amenities=self.amenities,
            furniture=self.furniture,
            furniture_status=self.furniture_status,
            key_lock_number=self.key_lock_number,
            juridical_status=self.juridical_status,
            exclusive=self.exclusive,
            is_hot=self.is_hot,
            has_3d=self.has_3d,
            has_media=self.has_media,
            has_photo=self.has_photo,
            content_assignee=self.content_assignee,
            content_assigner=self.content_assigner,
            request_content_time=self.request_content_time,
            content_status=self.content_status,
            media_assignee=self.media_assignee,
            media_assigner=self.media_assigner,
            media_status=self.media_status,
            rent_status=self.rent_status,
            rent_info=self.rent_info,
            sale_status=self.sale_status,
            house_status=self.house_status,
            property_status=self.property_status,
            transaction_stage=self.transaction_stage,
            core=Core.from_dict(yaml.safe_load(self.core)),
            address=Address.from_dict(yaml.safe_load(self.address)),
            contact=Contact.from_dict(yaml.safe_load(self.contact)),
            transaction=Transaction.from_dict(yaml.safe_load(self.transaction)),
            additional_info=additional_info,
            tags=self.tags,
            system_tags=self.system_tags,
            client_id=self.client_id,
            sap_id=self.sap_id,
            creator=self.creator,
            published_time=self.published_time,
            created_time=self.created_time,
            updated_time=self.updated_time,
            log_time=self.log_time,
        )

    def to_item_metadata(self) -> VhmItemMetadata:
        additional_info = AdditionalInfo.from_dict(yaml.safe_load(str(self.additional_info)))
        area, area_using, area_building = self._get_area_values(additional_info)
        width, length = self._get_width_length_values(additional_info)
        floors = self._get_floors_value(additional_info)
        sale_price, rent_price = self._get_price_values(additional_info)
        address = Address.from_dict(yaml.safe_load(self.address))
        core = Core.from_dict(yaml.safe_load(self.core))
        source_fund = core.source_fund if core else None
        return VhmItemMetadata.from_dict({
            "property_id": self.property_id,
            "alias": self.alias,
            # "name": self.name,
            "service_type": self.service_type,
            "business_type": self.business_type,
            "property_type": self.property_type,
            "project_id": self.project_id,
            "area_cluster_id": self.area_cluster_id,
            "building_id": self.building_id,
            "tower_id": self.tower_id,
            "delivery_type": self.delivery_type,
            "house_style": self.house_style,
            "architectural_style": self.architectural_style,
            "height_type": self.height_type,
            "sale_discount_vnd": self.sale_discount_vnd,
            "rent_discount_vnd": self.rent_discount_vnd,
            "sale_price_vnd": sale_price,
            "rent_price_vnd": rent_price,
            "num_bed_room": self.num_bed_room,
            "num_bath_room": self.num_bath_room,
            "floor_number": self.floor_number,
            "floors": floors,
            "direction": self.direction,
            "balcony_direction": self.balcony_direction,
            "area": area,
            "area_using": area_using,
            "area_building": area_building,
            "alleyway_width": self.alleyway_width,
            "width": width,
            "length": length,
            "corner": self.corner,
            "ownership": self.ownership,
            "furniture_status": self.furniture_status,
            "has_3d": self.has_3d,
            "has_media": self.has_media,
            "has_photo": self.has_photo,
            "house_status": self.house_status,
            "property_status": self.property_status,
            "transaction_stage": self.transaction_stage,
            "source_fund": source_fund,
            "city_id": address.city_id if address else None,
            "district_id": address.district_id if address else None,
            "ward_id": address.ward_id if address else None,
            "street_id": address.street_id if address else None
        }
        )

    def _get_area_values(self, additional_info: AdditionalInfo) -> Tuple[float, float, float]:
        additional_info_basic_area = 0
        additional_info_basic_area_using = 0
        additional_info_basic_area_building = 0
        if additional_info and additional_info.basic:
            additional_info_basic_area = additional_info.basic.area if additional_info.basic.area else 0
            additional_info_basic_area_using = additional_info.basic.area_using if additional_info.basic.area_using else 0
            additional_info_basic_area_building = additional_info.basic.area_building if additional_info.basic.area_building else 0

        _area, _area_using, _area_building = float(str(self.area)), float(str(self.area_using)), float(
            str(self.area_building))
        area = additional_info_basic_area if _area == 0 and additional_info_basic_area > 0 else _area
        area_using = additional_info_basic_area_using if _area_using == 0 and additional_info_basic_area_using > 0 else _area_using
        area_building = additional_info_basic_area_building if _area_building == 0 and additional_info_basic_area_building > 0 else _area_building

        return area, area_using, area_building

    def _get_width_length_values(self, additional_info: AdditionalInfo) -> Tuple[float, float]:
        additional_info_basic_width = 0
        additional_info_basic_length = 0
        if additional_info and additional_info.basic:
            additional_info_basic_width = additional_info.basic.width if additional_info.basic.width else 0
            additional_info_basic_length = additional_info.basic.length if additional_info.basic.length else 0
        _width, _length = float(str(self.width)), float(str(self.length))
        width = additional_info_basic_width if _width == 0 and additional_info_basic_width > 0 else _width
        length = additional_info_basic_length if _length == 0 and additional_info_basic_length > 0 else _length

        return width, length

    def _get_floors_value(self, additional_info: AdditionalInfo) -> int:
        additional_info_basic_floor = 0
        if additional_info and additional_info.basic:
            additional_info_basic_floor = additional_info.basic.floors if additional_info.basic.floors else 0
        _floors = int(str(self.floors))
        return additional_info_basic_floor if _floors == 0 and additional_info_basic_floor > 0 else _floors

    def _get_price_values(self, additional_info: AdditionalInfo) -> Tuple[float, float]:
        additional_info_basic_sale_price = 0
        additional_info_basic_rent_price = 0
        if additional_info and additional_info.basic:
            additional_info_basic_sale_price = additional_info.basic.sale_price if additional_info.basic.sale_price else 0
            additional_info_basic_rent_price = additional_info.basic.rent_price if additional_info.basic.rent_price else 0
        _sale_price = float(str(self.sale_price_vnd))
        _rent_price = float(str(self.rent_price_vnd))

        sale_price = additional_info_basic_sale_price if _sale_price == 0 and additional_info_basic_sale_price > 0 else _sale_price
        rent_price = additional_info_basic_rent_price if _rent_price == 0 and additional_info_basic_rent_price > 0 else _rent_price
        return sale_price, rent_price

    @staticmethod
    def from_dict(data: dict):
        c_data = {
            key: str(value).replace("\'", "\"").replace("None", "\"None\"")
            for key, value in data.items() if value is not None
        }
        exclusive = c_data.get("exclusive", None)
        exclusive = int(bool(exclusive)) if exclusive else None

        is_hot = c_data.get("is_hot", None)
        is_hot = int(bool(is_hot)) if is_hot else None

        is_verified = c_data.get("is_verified", None)
        is_verified = int(bool(is_verified)) if is_verified else None

        return VhmPropertyDmModel(
            property_id=c_data.get("property_id", None),
            vhm_id=c_data.get("vhm_id", None),
            alias=c_data.get("alias", None),
            name=c_data.get("name", None),
            full_name=c_data.get("full_name", None),
            service_type=c_data.get("service_type", None),
            property_type=c_data.get("property_type", None),
            transaction=(c_data.get("transaction", "{}")),
            sale_discount_vnd=c_data.get("sale_discount_vnd", None),
            rent_discount_vnd=c_data.get("rent_discount_vnd", None),
            sale_price_vnd=c_data.get("sale_price_vnd", None),
            rent_price_vnd=c_data.get("rent_price_vnd", None),
            num_bed_room=c_data.get("num_bed_room", None),
            num_bath_room=c_data.get("num_bath_room", None),
            area=c_data.get("area", None),
            area_using=c_data.get("area_using", None),
            area_building=c_data.get("area_building", None),
            alleyway_width=c_data.get("alleyway_width", None),
            width=c_data.get("width", None),
            length=c_data.get("length", None),
            floors=c_data.get("floors", None),
            exclusive=exclusive,
            is_hot=is_hot,
            is_verified=is_verified,
            architectural_style=c_data.get("architectural_style", None),
            direction=c_data.get("direction", None),
            balcony_direction=c_data.get("balcony_direction", None),
            amenities=c_data.get("amenities", "{}"),
            furniture=c_data.get("furniture", "{}"),
            ownership=c_data.get("ownership", None),
            contact=c_data.get("contact", "{}"),
            additional_info=c_data.get("additional_info", "{}"),
            owner_id=c_data.get("owner_id", None),
            team_id=c_data.get("team_id", None),
            market_center_id=c_data.get("market_center_id", None),
            key_lock_number=c_data.get("key_lock_number", None),
            project_id=c_data.get("project_id", None),
            address=c_data.get("address", "{}"),
            listing_opportunity_id=c_data.get("listing_opportunity_id", None),
            property_notes=c_data.get("property_notes", None),
            content_assignee=c_data.get("content_assignee", None),
            content_assigner=c_data.get("content_assigner", None),
            content_status=c_data.get("content_status", None),
            request_content_time=c_data.get("request_content_time", None),
            media_assignee=c_data.get("media_assignee", None),
            media_assigner=c_data.get("media_assigner", None),
            media_status=c_data.get("media_status", None),
            furniture_status=c_data.get("furniture_status", None),
            property_status=c_data.get("property_status", None),
            juridical_status=c_data.get("juridical_status", None),
            house_status=c_data.get("house_status", None),
            sale_status=c_data.get("sale_status", None),
            published_status=c_data.get("published_status", None),
            creator=c_data.get("creator", None),
            created_time=c_data.get("created_time", None),
            updated_time=c_data.get("updated_time", None),
            first_published_time=c_data.get("first_published_time", None),
            published_time=c_data.get("published_time", None),
            expired_time=c_data.get("expired_time", None),
            signed_time=c_data.get("signed_time", None),
            building_time=c_data.get("building_time", None),
            data_health_scoring_level=c_data.get("data_health_scoring_level", None),
            data_health_score=c_data.get("data_health_score", None),
            data_health_info=c_data.get("data_health_info", "{}"),
            delivery_type=c_data.get("delivery_type", None),
            house_style=c_data.get("house_style", None),
            log_time=c_data.get("log_time", None),
            business_type=c_data.get("business_type", None)
        )

class VhmContactDmModel:
    def __init__(
        self,
        hashed_phone: str = None,
        full_name: str = None,
        email_features: list = None,
        birthday: str = "",
        recent_source: str = None,
        temporary_address: str = "",
        temporary_standard_province: str = "",
        temporary_standard_district: str = "",
        temporary_standard_ward: str = "",
        permanent_address: str = "",
        permanent_standard_province: str = "",
        permanent_standard_district: str = "",
        permanent_standard_ward: str = "",
        pob_address: str = "",
        pob_standard_province: str = "",
        pob_standard_district: str = "",
        pob_standard_ward: str = "",
        hometown_address: str = "",
        hometown_standard_province: str = "",
        hometown_standard_district: str = "",
        hometown_standard_ward: str = "",
        geoip_address: str = "",
        geoip_standard_province: str = "",
        geoip_standard_district: str = "",
        geoip_standard_ward: str = "",
        contact_status: str = None,
        meeting_status: str = None,
        cid: str = "",
        cdp_profile_ids: list = None,
        created_time: int = 0,
        updated_time: int = 0,
        log_time: int = None
    ):
        self.hashed_phone = hashed_phone
        self.full_name = full_name
        self.email_features = email_features or []
        self.birthday = birthday
        self.recent_source = recent_source
        self.temporary_address = temporary_address
        self.temporary_standard_province = temporary_standard_province
        self.temporary_standard_district = temporary_standard_district
        self.temporary_standard_ward = temporary_standard_ward
        self.permanent_address = permanent_address
        self.permanent_standard_province = permanent_standard_province
        self.permanent_standard_district = permanent_standard_district
        self.permanent_standard_ward = permanent_standard_ward
        self.pob_address = pob_address
        self.pob_standard_province = pob_standard_province
        self.pob_standard_district = pob_standard_district
        self.pob_standard_ward = pob_standard_ward
        self.hometown_address = hometown_address
        self.hometown_standard_province = hometown_standard_province
        self.hometown_standard_district = hometown_standard_district
        self.hometown_standard_ward = hometown_standard_ward
        self.geoip_address = geoip_address
        self.geoip_standard_province = geoip_standard_province
        self.geoip_standard_district = geoip_standard_district
        self.geoip_standard_ward = geoip_standard_ward
        self.contact_status = contact_status
        self.meeting_status = meeting_status
        self.cid = cid
        self.cdp_profile_ids = cdp_profile_ids or []
        self.created_time = created_time
        self.updated_time = updated_time
        self.log_time = log_time

    @staticmethod
    def from_dict(data: dict):
        c_data = {
            key: str(value).replace("\'", "\"").replace("None", "\"None\"")
            for key, value in data.items() if value is not None
        }
        
        return VhmContactDmModel(
            hashed_phone=c_data.get("hashed_phone", None),
            full_name=c_data.get("full_name", None),
            email_features=c_data.get("email_features", []),
            birthday=c_data.get("birthday", ""),
            recent_source=c_data.get("recent_source", None),
            temporary_address=c_data.get("temporary_address", ""),
            temporary_standard_province=c_data.get("temporary_standard_province", ""),
            temporary_standard_district=c_data.get("temporary_standard_district", ""),
            temporary_standard_ward=c_data.get("temporary_standard_ward", ""),
            permanent_address=c_data.get("permanent_address", ""),
            permanent_standard_province=c_data.get("permanent_standard_province", ""),
            permanent_standard_district=c_data.get("permanent_standard_district", ""),
            permanent_standard_ward=c_data.get("permanent_standard_ward", ""),
            pob_address=c_data.get("pob_address", ""),
            pob_standard_province=c_data.get("pob_standard_province", ""),
            pob_standard_district=c_data.get("pob_standard_district", ""),
            pob_standard_ward=c_data.get("pob_standard_ward", ""),
            hometown_address=c_data.get("hometown_address", ""),
            hometown_standard_province=c_data.get("hometown_standard_province", ""),
            hometown_standard_district=c_data.get("hometown_standard_district", ""),
            hometown_standard_ward=c_data.get("hometown_standard_ward", ""),
            geoip_address=c_data.get("geoip_address", ""),
            geoip_standard_province=c_data.get("geoip_standard_province", ""),
            geoip_standard_district=c_data.get("geoip_standard_district", ""),
            geoip_standard_ward=c_data.get("geoip_standard_ward", ""),
            contact_status=c_data.get("contact_status", None),
            meeting_status=c_data.get("meeting_status", None),
            cid=c_data.get("cid", ""),
            cdp_profile_ids=c_data.get("cdp_profile_ids", []),
            created_time=c_data.get("created_time", 0),
            updated_time=c_data.get("updated_time", 0),
            log_time=c_data.get("log_time", None)
        )